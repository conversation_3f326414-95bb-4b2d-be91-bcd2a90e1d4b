# Technical Stack Specifications

## Overview
This document provides detailed technical specifications for the AI-powered internal chat agent system, based on a Node.js + TypeScript architecture.

## Backend Architecture

### Core Framework
- **Runtime**: Node.js (v18+ LTS)
- **Language**: TypeScript (v5.0+)
- **Web Framework**: Express.js (v4.18+)
- **API Design**: RESTful APIs + WebSocket for real-time chat

### Key Dependencies
```json
{
  "express": "^4.18.0",
  "typescript": "^5.0.0",
  "cors": "^2.8.5",
  "helmet": "^7.0.0",
  "express-rate-limit": "^6.7.0",
  "socket.io": "^4.7.0",
  "jsonwebtoken": "^9.0.0",
  "bcryptjs": "^2.4.3",
  "joi": "^17.9.0",
  "winston": "^3.8.0"
}
```

### Authentication & Security
- **OAuth2 Provider**: Integration with company identity provider
- **JWT Tokens**: For session management and API authentication
- **API Keys**: For CLI tool authentication
- **Rate Limiting**: Express rate limiting middleware
- **Security Headers**: Helmet.js for security headers
- **Input Validation**: Joi for request validation

## Database Architecture

### Primary Database
- **Platform**: Supabase (PostgreSQL 15+)
- **Vector Extension**: pgvector for embeddings storage
- **ORM**: Prisma (v5.0+) for type-safe database operations

### Database Schema Design
```typescript
// Core entities
interface User {
  id: string;
  email: string;
  name: string;
  role: 'user' | 'admin' | 'manager';
  createdAt: Date;
  updatedAt: Date;
}

interface Document {
  id: string;
  title: string;
  content: string;
  filePath: string;
  mimeType: string;
  uploadedBy: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ChatSession {
  id: string;
  userId: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Message {
  id: string;
  sessionId: string;
  content: string;
  role: 'user' | 'assistant';
  confidence?: number;
  escalated: boolean;
  createdAt: Date;
}

interface EscalatedQuery {
  id: string;
  messageId: string;
  status: 'open' | 'closed';
  assignedTo?: string;
  resolution?: string;
  createdAt: Date;
  resolvedAt?: Date;
}
```

### Vector Storage
- **Embeddings Model**: OpenAI text-embedding-ada-002
- **Vector Dimensions**: 1536 (OpenAI standard)
- **Similarity Search**: Cosine similarity via pgvector
- **Indexing**: HNSW index for efficient similarity search

## Frontend Architecture

### Web Application
- **Framework**: React (v18+) with TypeScript
- **Build Tool**: Vite (v4.0+)
- **State Management**: Zustand or React Query
- **UI Framework**: Tailwind CSS + Headless UI
- **Real-time**: Socket.io-client for live chat

### Key Frontend Dependencies
```json
{
  "react": "^18.2.0",
  "react-dom": "^18.2.0",
  "typescript": "^5.0.0",
  "vite": "^4.0.0",
  "tailwindcss": "^3.3.0",
  "@headlessui/react": "^1.7.0",
  "socket.io-client": "^4.7.0",
  "react-query": "^3.39.0",
  "react-router-dom": "^6.8.0",
  "react-hook-form": "^7.43.0"
}
```

## LLM Integration

### AI/ML Stack
- **Primary LLM**: OpenAI GPT-4 or GPT-3.5-turbo
- **Alternative LLM**: Anthropic Claude (configurable)
- **Embeddings**: OpenAI text-embedding-ada-002
- **RAG Framework**: Custom implementation using LangChain.js

### LLM Dependencies
```json
{
  "openai": "^4.0.0",
  "@anthropic-ai/sdk": "^0.6.0",
  "langchain": "^0.0.140",
  "@langchain/openai": "^0.0.14",
  "@langchain/community": "^0.0.25"
}
```

### RAG Implementation
- **Document Chunking**: Recursive character text splitter
- **Chunk Size**: 1000 characters with 200 character overlap
- **Retrieval**: Top-k similarity search (k=5)
- **Context Window**: 4000 tokens for GPT-3.5, 8000 for GPT-4
- **Confidence Scoring**: Custom confidence calculation based on retrieval scores

## Document Processing

### File Processing Stack
```json
{
  "pdf-parse": "^1.1.1",
  "mammoth": "^1.5.1",
  "multer": "^1.4.5",
  "sharp": "^0.32.0",
  "file-type": "^18.5.0"
}
```

### Supported Formats
- **PDF**: pdf-parse for text extraction
- **Word Documents**: mammoth.js for .docx files
- **Text Files**: Direct processing (.txt, .md)
- **Images**: OCR capability using Tesseract.js (future enhancement)

### Processing Pipeline
1. File upload validation (type, size limits)
2. Text extraction based on file type
3. Content cleaning and preprocessing
4. Text chunking for embeddings
5. Vector generation and storage
6. Metadata indexing

## Slack Integration

### Slack Bot Framework
```json
{
  "@slack/bolt": "^3.13.0",
  "@slack/web-api": "^6.8.0"
}
```

### Bot Capabilities
- **Slash Commands**: `/ask` for direct queries
- **Direct Messages**: Full conversation support
- **Channel Integration**: Mention-based responses
- **Interactive Components**: Buttons for escalation/feedback

## Command Line Interface

### CLI Framework
```json
{
  "commander": "^10.0.0",
  "inquirer": "^9.1.0",
  "chalk": "^5.2.0",
  "ora": "^6.1.0",
  "table": "^6.8.0"
}
```

### CLI Commands Structure
```bash
ichat-cli auth login                    # Authenticate with API key
ichat-cli docs upload <file>            # Upload document
ichat-cli docs list                     # List all documents
ichat-cli docs delete <id>              # Delete document
ichat-cli users create <email>          # Create user
ichat-cli users list                    # List users
ichat-cli chat start                    # Start interactive chat
ichat-cli queries list                  # List escalated queries
ichat-cli queries respond <id>          # Respond to escalated query
ichat-cli system status                 # System health check
```

## API Design

### REST Endpoints
```typescript
// Authentication
POST /api/auth/login
POST /api/auth/refresh
POST /api/auth/logout

// Documents
GET /api/documents
POST /api/documents/upload
PUT /api/documents/:id
DELETE /api/documents/:id

// Chat
GET /api/chat/sessions
POST /api/chat/sessions
GET /api/chat/sessions/:id/messages
POST /api/chat/sessions/:id/messages

// Users (Admin only)
GET /api/users
POST /api/users
PUT /api/users/:id
DELETE /api/users/:id

// Escalated Queries
GET /api/queries/escalated
POST /api/queries/:id/respond
PUT /api/queries/:id/status

// System
GET /api/health
GET /api/metrics
```

### WebSocket Events
```typescript
// Client to Server
'chat:join_session'
'chat:send_message'
'chat:typing'

// Server to Client
'chat:message_received'
'chat:typing_indicator'
'chat:escalation_created'
'system:notification'
```
